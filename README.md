# 🕌 Akhuwat - Islamic Charity App

<div align="center">
  <img src="assets/images/applogo.png" alt="Akhuwat Logo" width="120" height="120">
  
  [![Flutter](https://img.shields.io/badge/Flutter-3.x-blue.svg)](https://flutter.dev/)
  [![Dart](https://img.shields.io/badge/Dart-3.x-blue.svg)](https://dart.dev/)
  [![License](https://img.shields.io/badge/License-Private-red.svg)]()
  [![Platform](https://img.shields.io/badge/Platform-iOS%20%7C%20Android%20%7C%20Web%20%7C%20Desktop-lightgrey.svg)]()
</div>

## 📖 Overview

**Akhuwat** is a comprehensive Islamic charity application built with Flutter, designed to facilitate seamless charitable donations in accordance with Islamic principles. The app provides a modern, user-friendly interface for Muslims to fulfill their religious obligations and voluntary charitable acts through secure digital payments.
### 🎯 Mission Statement
We are dedicated to serving humanity through charitable initiatives, promoting social welfare, and fostering community development in accordance with Islamic principles.

## ✨ Key Features

### 🎁 Donation Management
- **Multiple Donation Types**: Support for various Islamic charitable categories
  - **Zakat** - Annual obligatory charity (2.5% of wealth)
  - **Sadaqah** - Voluntary charity at any time
  - **Fidya** - Compensation for missed religious obligations
  - **Kaffarah** - Atonement for specific religious violations
  - **Aqeeqah** - Charity for newborn children
  - **General** - Unrestricted charitable donations

### 🏛️ Campaign System
- Browse active charitable campaigns
- Donate directly to specific causes
- Real-time campaign progress tracking
- Featured and trending campaigns

### 👤 User Profile & Analytics
- Comprehensive donation history
- Monthly activity summaries
- Transaction tracking and receipts
- Personal donation statistics

### 📊 Reports (Admin Users Only)
- Advanced reporting system
- Transaction analytics by date range

### 🌍 Multi-Platform Support
- **Mobile**: iOS and Android native apps
- **Web**: Progressive Web App (PWA)
- **Desktop**: Windows, macOS, and Linux support

### 🌐 Internationalization
- **English** (Primary)
- **Swedish** (Svenska)
- Easy language switching
- RTL support ready for Arabic

## 🏗️ Architecture & Technical Stack

### Frontend (Flutter)
```
lib/
├── core/                     # Core application logic
│   ├── constants/           # API endpoints and app constants
│   ├── models/             # Data models and entities
│   ├── services/           # Business logic and API services
│   ├── providers/          # State management providers
│   ├── theme/              # App theming and styling
│   ├── utils/              # Utility functions and helpers
│   └── presentation/       # Shared UI components
├── features/               # Feature-based modules
│   ├── auth/              # Authentication (login/register)
│   ├── home/              # Dashboard and quick actions
│   ├── donate/            # Donation flow and forms
│   ├── campaigns/         # Campaign browsing and details
│   ├── profile/           # User profile and statistics
│   ├── reports/           # Admin reporting (admin only)
│   ├── payment/           # Payment processing and WebView
│   ├── notifications/     # Push notifications
│   └── about/             # App information
├── l10n/                  # Localization files
├── routes/                # Navigation and routing
└── main.dart              # Application entry point
```

### Key Dependencies
- **State Management**: Provider pattern with ChangeNotifier
- **HTTP Client**: Dio for API communication
- **Secure Storage**: Flutter Secure Storage for tokens
- **Localization**: Flutter's built-in i18n support
- **UI Components**: Material Design 3
- **Payment Integration**: WebView for Worldline/EftaaPay

## 🔄 Payment Flow Architecture

### 1. User Initiation
```
User selects donation type → Enters amount → Chooses anonymity → Clicks donate
```

### 2. Payment Creation
```
Flutter App → Charity Backend API → EftaaPay Payment Gateway
```

### 3. Transaction Processing
```
EftaaPay creates transaction → Returns transaction ID → Charity backend saves to DB
```

### 4. Checkout Redirect
```
User redirected to Worldline checkout → Selects payment method → Completes payment
```

### 5. Return Flow
```
Payment success/failure → User returns to app → Success/failure screen displayed
```

## 🔐 Authentication & Security

### Token Management
- **JWT Authentication**: 24-hour token validity
- **Secure Storage**: Tokens stored using Flutter Secure Storage
- **Auto-refresh**: Seamless token renewal
- **Logout Protection**: Secure token cleanup

### API Security
- Bearer token authentication
- HTTPS-only communication
- Request/response encryption

## 🚀 Getting Started

### Prerequisites
- **Flutter SDK**: 3.x or higher
- **Dart SDK**: 3.x or higher
- **IDE**: VS Code, Android Studio, or IntelliJ IDEA
- **Platform Tools**: 
  - Android: Android Studio & SDK
  - iOS: Xcode (macOS only)
  - Web: Chrome browser
  - Desktop: Platform-specific build tools

📌 **Additional Resources**
- [Confluence Documentation](https://eftaapay.atlassian.net/wiki/spaces/PAD/pages/134807905/Akhuwat+-+Islamic+Charity+App+Documentation?atlOrigin=eyJpIjoiMTg3OGZhYmEwNjg2NGQwNzhlMDkxOWIwNWY2YzQxNzIiLCJwIjoiYyJ9)
- [GitHub Repository](https://github.com/Eftaa-Pay/eftaapay-charity-app)


### Installation

1. **Clone the Repository**
```bash
git clone https://github.com/Eftaa-Pay/eftaapay-charity-app
cd islamic_charity_app
```

2. **Install Dependencies**
```bash
flutter pub get
```

3. **Generate Localization Files**
```bash
flutter gen-l10n
```

4. **Configure API Endpoints**
   - Open `lib/core/constants/api_constants.dart`
   - Update base URLs for your environment:
   ```dart
   static final baseUrl = 'http://your-api-server:3000';
   ```

5. **Run the Application**
```bash
# Development mode
flutter run

# Specific platform
flutter run -d chrome          # Web
flutter run -d windows         # Windows
flutter run -d macos           # macOS
flutter run -d linux           # Linux
```

### Build for Production

```bash
# Android APK
flutter build apk --release

# Android App Bundle
flutter build appbundle --release

# iOS
flutter build ios --release

# Web
flutter build web --release

# Windows
flutter build windows --release

# macOS
flutter build macos --release

# Linux
flutter build linux --release
```

## ⚙️ Configuration

### API Configuration
All API endpoints are centralized in `lib/core/constants/api_constants.dart`:

### Environment Setup
- **No environment variables required**

### Platform-Specific Configuration

#### Android
- Minimum SDK: 21 (Android 5.0)
- Target SDK: Latest stable
- Permissions: Internet, network state

#### iOS
- Minimum iOS: 12.0
- Deployment target: Latest stable
- Permissions: Network access

#### Web
- PWA enabled
- Responsive design

## 📱 Platform Support

| Platform | Status | Notes |
|----------|--------|-------|
| Android | ✅ Full Support | API 21+ |
| iOS | ✅ Full Support | iOS 12.0+ |
| Web | ✅ Full Support | PWA enabled |
| Windows | ✅ Full Support | Windows 10+ |
| macOS | ✅ Full Support | macOS 10.14+ |
| Linux | ✅ Full Support | Ubuntu 18.04+ |

## 🌐 Localization

### Supported Languages
- **English** (`en`) - Primary language
- **Swedish** (`sv`) - Secondary language

### Adding New Languages
1. Create new ARB file: `lib/l10n/app_[locale].arb`
2. Add translations following existing structure
3. Update `supportedLocales` in `main.dart`
4. Run `flutter gen-l10n` to generate classes

## 🔧 Development Guidelines

### Code Structure
- **Feature-first architecture**: Organized by features, not layers
- **Clean architecture principles**: Separation of concerns
- **Provider pattern**: For state management
- **Repository pattern**: For data access

### Naming Conventions
- **Files**: snake_case
- **Classes**: PascalCase
- **Variables/Functions**: camelCase
- **Constants**: UPPER_SNAKE_CASE

### Git Workflow
```bash
# Feature development
git checkout -b feature/donation-improvements
git commit -m "feat: add donation amount validation"
git push origin feature/donation-improvements
```

## 🚀 Deployment

### Backend Requirements
- **Charity Backend API**: Nestjs.js/Express server
- **Database**: MySQL
- **Payment Gateway**: EftaaPay Payment Backend with Worldline integration
- **Authentication**: JWT token system

### Frontend Deployment

#### Mobile App Stores
- **Google Play Store**: Android App Bundle
- **Apple App Store**: iOS Archive

## 📊 Performance Optimization

### App Performance
- **Lazy loading**: Features loaded on demand
- **State management**: Efficient provider usage

### Build Optimization
```bash
# Optimized release build
flutter build apk --release --shrink --obfuscate --split-debug-info=debug-info/
```

## 🛠️ Troubleshooting

### Common Issues

#### Build Errors
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter build
```

#### API Connection Issues
- Check `api_constants.dart` URLs
- Verify network connectivity
- Check backend server status

#### Platform-Specific Issues
- **Android**: Check SDK versions and permissions
- **iOS**: Verify Xcode and provisioning profiles
- **Web**: Check CORS settings on backend

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create feature branch
3. Follow coding standards
4. Add tests for new features
5. Submit pull request

### Code Review Process
- All changes require review
- Automated testing must pass
- Documentation updates required
- Performance impact assessment

## 📄 License

This project is proprietary software. All rights reserved.

---

<div align="center">
  <p><strong>Built with ❤️ for the Muslim community</strong></p>
  <p><em>"The believer is not one who eats his fill while his neighbor goes hungry." - Prophet Muhammad (PBUH)</em></p>
</div>