import '../utils/number_formatter.dart';

class Campaign {
  final String id;
  final String title;
  final String description;
  final String goalAmount;
  final String raisedAmount;
  final DateTime startDate;
  final DateTime endDate;
  final String? imageUrl;
  final String status;
  final CampaignUser user;
  final DateTime createdAt;
  final DateTime updatedAt;

  Campaign({
    required this.id,
    required this.title,
    required this.description,
    required this.goalAmount,
    required this.raisedAmount,
    required this.startDate,
    required this.endDate,
    this.imageUrl,
    required this.status,
    required this.user,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Campaign.fromJson(Map<String, dynamic> json) {
    return Campaign(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      goalAmount: json['goalAmount'] ?? '0.00',
      raisedAmount: json['raisedAmount'] ?? '0.00',
      startDate: DateTime.parse(json['startDate'] ?? DateTime.now().toIso8601String()),
      endDate: DateTime.parse(json['endDate'] ?? DateTime.now().toIso8601String()),
      imageUrl: json['imageUrl'],
      status: json['status'] ?? '',
      user: CampaignUser.fromJson(json['user'] ?? {}),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'goalAmount': goalAmount,
      'raisedAmount': raisedAmount,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'imageUrl': imageUrl,
      'status': status,
      'user': user.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Helper methods
  double get goalAmountValue => (double.tryParse(goalAmount) ?? 0.0) / 100;
  double get raisedAmountValue => (double.tryParse(raisedAmount) ?? 0.0) / 100;

  String get formattedGoalAmount => NumberFormatter.formatAmountWithCurrency(goalAmountValue, decimalPlaces: 0);
  String get formattedRaisedAmount => NumberFormatter.formatAmountWithCurrency(raisedAmountValue, decimalPlaces: 0);
  
  double get progressPercentage {
    if (goalAmountValue == 0) return 0.0;
    return (raisedAmountValue / goalAmountValue).clamp(0.0, 1.0);
  }
  
  int get daysLeft {
    final now = DateTime.now();
    if (endDate.isBefore(now)) return 0;
    return endDate.difference(now).inDays;
  }
  
  String get formattedDaysLeft {
    final days = daysLeft;
    if (days == 0) return 'Ended';
    if (days == 1) return '1 day left';
    return '$days days left';
  }
  
  String get formattedStartDate {
    return '${startDate.day}/${startDate.month}/${startDate.year}';
  }
  
  String get formattedEndDate {
    return '${endDate.day}/${endDate.month}/${endDate.year}';
  }
}

class CampaignUser {
  final String id;
  final String name;
  final String email;

  CampaignUser({
    required this.id,
    required this.name,
    required this.email,
  });

  factory CampaignUser.fromJson(Map<String, dynamic> json) {
    return CampaignUser(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
    };
  }
}
