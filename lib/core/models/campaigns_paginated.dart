import 'campaign.dart';

class CampaignsPaginated {
  final List<Campaign> campaigns;
  final int totalCount;
  final int currentPage;
  final int totalPages;
  final int currentPageCount;

  CampaignsPaginated({
    required this.campaigns,
    required this.totalCount,
    required this.currentPage,
    required this.totalPages,
    required this.currentPageCount,
  });

  factory CampaignsPaginated.fromJson(Map<String, dynamic> json) {
    return CampaignsPaginated(
      campaigns: (json['campaigns'] as List<dynamic>?)
              ?.map((campaign) => Campaign.fromJson(campaign))
              .toList() ??
          [],
      totalCount: json['totalCount'] ?? 0,
      currentPage: json['currentPage'] ?? 1,
      totalPages: json['totalPages'] ?? 1,
      currentPageCount: json['currentPageCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'campaigns': campaigns.map((campaign) => campaign.toJson()).toList(),
      'totalCount': totalCount,
      'currentPage': currentPage,
      'totalPages': totalPages,
      'currentPageCount': currentPageCount,
    };
  }

  bool get hasMorePages => currentPage < totalPages;
  bool get isEmpty => campaigns.isEmpty;
  bool get isNotEmpty => campaigns.isNotEmpty;
}
