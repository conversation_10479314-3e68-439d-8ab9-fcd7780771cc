class PaymentRequest {
  final double amount;
  final String currencyCode;
  final String locale;
  final String returnUrl;
  final String transactionReference;
  final String type;
  final bool isAnonymous;
  final String? campaignId;

  PaymentRequest({
    required this.amount,
    required this.currencyCode,
    required this.locale,
    required this.returnUrl,
    required this.transactionReference,
    required this.type,
    required this.isAnonymous,
    this.campaignId,
  });

  Map<String, dynamic> toJson() {
    final json = {
      'amount': amount,
      'currencyCode': currencyCode,
      'locale': locale,
      'returnUrl': returnUrl,
      'transactionReference': transactionReference,
      'type': type,
      'isAnonymous': isAnonymous,
    };

    if (campaignId != null) {
      json['campaignId'] = campaignId!;
    }

    return json;
  }
}

class PaymentResponse {
  final bool status;
  final String message;
  final String? data; // This will be the UUID

  PaymentResponse({
    required this.status,
    required this.message,
    this.data,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentResponse(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data: json['data'],
    );
  }

  bool get isSuccess => status && data != null;
  String? get transactionId => data;
}

enum PaymentStatus {
  pending,
  success,
  failed,
  cancelled,
}

class PaymentResult {
  final PaymentStatus status;
  final String? transactionId;
  final String? externalId;
  final String? currency;
  final String? message;
  final DateTime? updatedAt;

  PaymentResult({
    required this.status,
    this.transactionId,
    this.externalId,
    this.currency,
    this.message,
    this.updatedAt,
  });

  bool get isSuccess => status == PaymentStatus.success;
  bool get isFailed => status == PaymentStatus.failed;
  bool get isCancelled => status == PaymentStatus.cancelled;
}
