import '../utils/number_formatter.dart';

class Transaction {
  final String id;
  final String amount;
  final String transactionId;
  final String status;
  final String type;
  final bool isAnonymous;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String transactionReference;
  final TransactionCampaign? campaign;

  Transaction({
    required this.id,
    required this.amount,
    required this.transactionId,
    required this.status,
    required this.type,
    required this.isAnonymous,
    required this.createdAt,
    required this.updatedAt,
    required this.transactionReference,
    this.campaign,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'] ?? '',
      amount: json['amount'] ?? '0.00',
      transactionId: json['transactionId'] ?? '',
      status: json['status'] ?? '',
      type: json['type'] ?? '',
      isAnonymous: (json['isAnonymous'] == 1) || (json['isAnonymous'] == true),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      transactionReference: json['transactionReference'] ?? '',
      campaign: json['campaign'] != null ? TransactionCampaign.fromJson(json['campaign']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'transactionId': transactionId,
      'status': status,
      'type': type,
      'isAnonymous': isAnonymous,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'transactionReference': transactionReference,
      'campaign': campaign?.toJson(),
    };
  }

  // Helper methods
  double get amountValue => (double.tryParse(amount) ?? 0.0) / 100;
  String get formattedAmount => NumberFormatter.formatAmountWithCurrency(amountValue);
  
  String get formattedDate {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }
  
  String get formattedTime {
    return '${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}';
  }

  String get formattedDateTime {
    return '$formattedDate at $formattedTime';
  }
  
  bool get isAnonymousTransaction => isAnonymous;
  
  String get statusDisplayText {
    switch (status.toUpperCase()) {
      case 'CAPTURED':
        return 'Completed';
      case 'PENDING':
        return 'Pending';
      case 'FAILED':
        return 'Failed';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status;
    }
  }
}

class TransactionCampaign {
  final String id;
  final String name;

  TransactionCampaign({
    required this.id,
    required this.name,
  });

  factory TransactionCampaign.fromJson(Map<String, dynamic> json) {
    return TransactionCampaign(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}

class TransactionsPaginated {
  final List<Transaction> donations;
  final int totalCount;
  final int currentPage;
  final int totalPages;
  final int currentPageCount;

  TransactionsPaginated({
    required this.donations,
    required this.totalCount,
    required this.currentPage,
    required this.totalPages,
    required this.currentPageCount,
  });

  factory TransactionsPaginated.fromJson(Map<String, dynamic> json) {
    return TransactionsPaginated(
      donations: (json['donations'] as List<dynamic>?)
              ?.map((donation) => Transaction.fromJson(donation))
              .toList() ??
          [],
      totalCount: json['totalCount'] ?? 0,
      currentPage: json['currentPage'] ?? 1,
      totalPages: json['totalPages'] ?? 1,
      currentPageCount: json['currentPageCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'donations': donations.map((donation) => donation.toJson()).toList(),
      'totalCount': totalCount,
      'currentPage': currentPage,
      'totalPages': totalPages,
      'currentPageCount': currentPageCount,
    };
  }

  bool get hasMorePages => currentPage < totalPages;

  // Calculate total amount for current page
  double get totalAmount {
    return donations.fold(0.0, (sum, transaction) => sum + transaction.amountValue);
  }
}
