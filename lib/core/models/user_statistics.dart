class UserStatistics {
  final double totalDonationsAmount;
  final int totalCharities;
  final int totalEvents;

  UserStatistics({
    required this.totalDonationsAmount,
    required this.totalCharities,
    required this.totalEvents,
  });

  factory UserStatistics.fromJson(Map<String, dynamic> json) {
    return UserStatistics(
      totalDonationsAmount: ((json['totalDonationsAmount'] ?? 0.0).toDouble()) / 100,
      totalCharities: json['totalCharities'] ?? 0,
      totalEvents: json['totalEvents'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalDonationsAmount': totalDonationsAmount,
      'totalCharities': totalCharities,
      'totalEvents': totalEvents,
    };
  }
}


