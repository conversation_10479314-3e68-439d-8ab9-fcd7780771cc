import 'package:flutter/material.dart';
import 'package:islamic_charity_app/l10n/app_localizations.dart';
import '../../../features/home/<USER>/pages/home_page.dart';
import '../../../features/profile/presentation/pages/profile_page.dart';
import '../../../features/campaigns/presentation/pages/campaigns_page.dart';
import '../../../features/reports/presentation/pages/reports_page.dart';
import '../../services/auth_service.dart';

class MainNavigationPage extends StatefulWidget {
  const MainNavigationPage({super.key});

  @override
  State<MainNavigationPage> createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends State<MainNavigationPage> {
  int _currentIndex = 0;
  bool _isAdmin = false;
  List<Widget> _pages = [];

  @override
  void initState() {
    super.initState();
    _checkUserRole();
  }

  Future<void> _checkUserRole() async {
    final userData = await AuthService().getCurrentUser();
    final userRole = userData?['role'] as String?;

    setState(() {
      _isAdmin = userRole == 'ADMIN';
      _pages = _buildPages();
    });
  }

  List<Widget> _buildPages() {
    final pages = [
      const HomePageContent(), // Home content without bottom nav
      const CampaignsPageContent(), // Campaigns page content
      const ComingSoonPage(title: 'Events'), // Events placeholder
    ];

    if (_isAdmin) {
      pages.add(const ReportsPageContent()); // Reports page for admin
    }

    pages.add(const ProfilePageContent()); // Profile content without app bar

    return pages;
  }

  void _onDestinationSelected(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  void _showComingSoonDialog(BuildContext context, String featureName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(
          Icons.construction,
          size: 48,
          color: Colors.orange,
        ),
        title: const Text('Coming Soon!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '$featureName is currently under development.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'We\'re working hard to bring you this feature soon!',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it!'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      body: SafeArea(
        top: false, // Let individual pages handle their own top SafeArea
        child: IndexedStack(
          index: _currentIndex,
          children: _pages,
        ),
      ),
      bottomNavigationBar: NavigationBar(
        selectedIndex: _currentIndex,
        onDestinationSelected: (index) {
          if (index == 2) {
            // Show coming soon dialog for events only
            _showComingSoonDialog(context, l10n.events);
          } else {
            _onDestinationSelected(index);
          }
        },
        destinations: _buildNavigationDestinations(l10n),
      ),
    );
  }

  List<NavigationDestination> _buildNavigationDestinations(AppLocalizations l10n) {
    final destinations = [
      NavigationDestination(
        icon: const Icon(Icons.home_outlined),
        selectedIcon: const Icon(Icons.home),
        label: l10n.home,
      ),
      NavigationDestination(
        icon: const Icon(Icons.volunteer_activism_outlined),
        selectedIcon: const Icon(Icons.volunteer_activism),
        label: l10n.campaigns,
      ),
      NavigationDestination(
        icon: Stack(
          children: [
            const Icon(Icons.event_outlined),
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: const BoxDecoration(
                  color: Colors.orange,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.schedule,
                  size: 8,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        selectedIcon: const Icon(Icons.event),
        label: l10n.events,
      ),
    ];

    if (_isAdmin) {
      destinations.add(
        const NavigationDestination(
          icon: Icon(Icons.analytics_outlined),
          selectedIcon: Icon(Icons.analytics),
          label: 'Reports',
        ),
      );
    }

    destinations.add(
      NavigationDestination(
        icon: const Icon(Icons.person_outline),
        selectedIcon: const Icon(Icons.person),
        label: l10n.profile,
      ),
    );

    return destinations;
  }
}

// Home page content without the bottom navigation
class HomePageContent extends StatefulWidget {
  const HomePageContent({super.key});

  @override
  State<HomePageContent> createState() => _HomePageContentState();
}

class _HomePageContentState extends State<HomePageContent> {
  @override
  Widget build(BuildContext context) {
    return HomePage(
      key: homePageKey,
      showBottomNav: false,
      onNavigateToCampaigns: () {
        // Navigate to campaigns tab in the main navigation
        final mainNavState = context.findAncestorStateOfType<_MainNavigationPageState>();
        mainNavState?._onDestinationSelected(1);
      },
    );
  }
}

// Campaigns page content
class CampaignsPageContent extends StatelessWidget {
  const CampaignsPageContent({super.key});

  @override
  Widget build(BuildContext context) {
    return CampaignsPage(key: campaignsPageKey);
  }
}

// Reports page content
class ReportsPageContent extends StatelessWidget {
  const ReportsPageContent({super.key});

  @override
  Widget build(BuildContext context) {
    return const ReportsPage();
  }
}

// Profile page content without the app bar
class ProfilePageContent extends StatelessWidget {
  const ProfilePageContent({super.key});

  @override
  Widget build(BuildContext context) {
    return const ProfilePage(showAppBar: false);
  }
}

// Coming soon placeholder page
class ComingSoonPage extends StatelessWidget {
  final String title;
  
  const ComingSoonPage({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          title,
          style: TextStyle(
            fontSize: (screenWidth * 0.05).clamp(16.0, 20.0), // Responsive font size
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(screenWidth * 0.06), // Responsive padding
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.construction,
                  size: (screenWidth * 0.2).clamp(60.0, 100.0), // Responsive icon size
                  color: Colors.orange,
                ),
                SizedBox(height: screenHeight * 0.03), // Responsive spacing
                Text(
                  'Coming Soon!',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontSize: (screenWidth * 0.06).clamp(20.0, 28.0), // Responsive font size
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: screenHeight * 0.02), // Responsive spacing
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text(
                    '$title is currently under development.',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontSize: (screenWidth * 0.04).clamp(14.0, 18.0), // Responsive font size
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(height: screenHeight * 0.01), // Responsive spacing
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text(
                    'We\'re working hard to bring you this feature soon!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                      fontSize: (screenWidth * 0.035).clamp(12.0, 16.0), // Responsive font size
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
