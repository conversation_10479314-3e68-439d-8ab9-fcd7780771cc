import 'package:dio/dio.dart';
import '../constants/api_constants.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'token_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiService _apiService = ApiService();

  /// Register a new user
  Future<ApiResponse> register({
    required String name,
    required String email,
    required String password,
  }) async {
    try {
      final response = await _apiService.post(
        ApiConstants.registerEndpoint,
        data: {
          'name': name,
          'email': email,
          'password': password,
        },
      );

      final apiResponse = ApiResponse.fromJson(response.data);

      // Store token and user data if registration successful
      if (apiResponse.isSuccess && apiResponse.data != null) {
        final data = apiResponse.data as Map<String, dynamic>;
        if (data.containsKey('token') && data.contains<PERSON>ey('user')) {
          await TokenService.storeAuthData(
            token: data['token'],
            userData: data['user'],
          );
        }
      }

      return apiResponse;
    } on DioException catch (e) {
      // Extract error message from API response
      String errorMessage = 'An unexpected error occurred. Please try again.';
      if (e.response?.data != null && e.response!.data is Map<String, dynamic>) {
        final responseData = e.response!.data as Map<String, dynamic>;
        errorMessage = responseData['message'] ?? errorMessage;
      }
      return ApiResponse(
        status: false,
        message: errorMessage,
        data: null,
      );
    } catch (e) {
      return ApiResponse(
        status: false,
        message: 'An unexpected error occurred. Please try again.',
        data: null,
      );
    }
  }

  /// Login user
  Future<ApiResponse> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _apiService.post(
        ApiConstants.loginEndpoint,
        data: {
          'email': email,
          'password': password,
        },
      );

      final apiResponse = ApiResponse.fromJson(response.data);

      // Store token and user data if login successful
      if (apiResponse.isSuccess && apiResponse.data != null) {
        final data = apiResponse.data as Map<String, dynamic>;
        if (data.containsKey('token') && data.containsKey('user')) {
          await TokenService.storeAuthData(
            token: data['token'],
            userData: data['user'],
          );
        }
      }

      return apiResponse;
    } on DioException catch (e) {
      // Extract error message from API response
      String errorMessage = 'An unexpected error occurred. Please try again.';
      if (e.response?.data != null && e.response!.data is Map<String, dynamic>) {
        final responseData = e.response!.data as Map<String, dynamic>;
        errorMessage = responseData['message'] ?? errorMessage;
      }
      return ApiResponse(
        status: false,
        message: errorMessage,
        data: null,
      );
    } catch (e) {
      return ApiResponse(
        status: false,
        message: 'An unexpected error occurred. Please try again.',
        data: null,
      );
    }
  }
  
  /// Logout user
  Future<void> logout() async {
    await TokenService.clearAuthData();
  }
  
  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    return await TokenService.isTokenValid();
  }
  
  /// Get current user data
  Future<Map<String, dynamic>?> getCurrentUser() async {
    return await TokenService.getUserData();
  }

  /// Get current user ID
  Future<String?> getUserId() async {
    final userData = await getCurrentUser();
    return userData?['id'] as String?;
  }


}
