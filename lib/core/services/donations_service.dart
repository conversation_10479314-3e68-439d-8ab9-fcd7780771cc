import 'package:dio/dio.dart';
import '../constants/api_constants.dart';
import '../models/api_response.dart';
import '../models/transaction.dart';
import 'api_service.dart';

class DonationsService {
  static final DonationsService _instance = DonationsService._internal();
  factory DonationsService() => _instance;
  DonationsService._internal();

  final ApiService _apiService = ApiService();

  /// Get paginated user donations
  Future<ApiResponse<TransactionsPaginated>> getUserDonationsPaginated({
    int page = 1,
    int limit = 10,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParameters = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      // Add date filters if provided
      if (startDate != null) {
        queryParameters['startDate'] = startDate.toIso8601String().split('T')[0];
      }
      if (endDate != null) {
        queryParameters['endDate'] = endDate.toIso8601String().split('T')[0];
      }

      final response = await _apiService.get(
        ApiConstants.userDonationsPaginatedEndpoint,
        queryParameters: queryParameters,
      );

      // The API returns the data directly without wrapping in a standard ApiResponse
      // So we need to handle it differently
      final transactionsPaginated = TransactionsPaginated.fromJson(response.data);

      return ApiResponse<TransactionsPaginated>(
        status: true,
        message: 'Transactions retrieved successfully',
        data: transactionsPaginated,
      );
    } on DioException catch (e) {
      String errorMessage = 'Failed to load transactions. Please try again.';
      if (e.response?.data != null && e.response!.data is Map<String, dynamic>) {
        final responseData = e.response!.data as Map<String, dynamic>;
        errorMessage = responseData['message'] ?? errorMessage;
      }
      return ApiResponse<TransactionsPaginated>(
        status: false,
        message: errorMessage,
        data: null,
      );
    } catch (e) {
      return ApiResponse<TransactionsPaginated>(
        status: false,
        message: 'An unexpected error occurred. Please try again.',
        data: null,
      );
    }
  }


}
