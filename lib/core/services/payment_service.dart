import 'package:dio/dio.dart';
import '../constants/api_constants.dart';
import '../models/payment_models.dart';
import 'api_service.dart';
import 'token_service.dart';

class PaymentService {
  static final PaymentService _instance = PaymentService._internal();
  factory PaymentService() => _instance;
  PaymentService._internal();

  /// Create a payment transaction
  Future<PaymentResponse> createPayment({
    required double amount,
    String currencyCode = 'SEK',
    String locale = 'en_GB',
    String? transactionReference,
    required String type,
    required bool isAnonymous,
    String? campaignId,
  }) async {
    try {
      // Get JWT token from storage
      final token = await TokenService.getToken();
      if (token == null) {
        return PaymentResponse(
          status: false,
          message: 'User not authenticated',
        );
      }

      // Create payment request
      final paymentRequest = PaymentRequest(
        amount: amount,
        currencyCode: currencyCode,
        locale: locale,
        returnUrl: ApiConstants.paymentReturnBaseUrl,
        transactionReference: transactionReference ?? _generateMerchantReference(),
        type: type,
        isAnonymous: isAnonymous,
        campaignId: campaignId,
      );

      // Make API call with custom headers to include JWT
      final dio = Dio();
      final response = await dio.post(
        ApiConstants.createPaymentUrl,
        data: paymentRequest.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      return PaymentResponse.fromJson(response.data);
    } on DioException catch (e) {
      String errorMessage = 'Payment creation failed';
      
      if (e.response != null) {
        final responseData = e.response!.data;
        if (responseData is Map<String, dynamic> && responseData.containsKey('message')) {
          errorMessage = responseData['message'];
        } else {
          errorMessage = 'Server error: ${e.response!.statusCode}';
        }
      } else if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = 'Connection timeout. Please check your internet connection.';
      } else if (e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Request timeout. Please try again.';
      } else {
        errorMessage = 'Network error. Please try again.';
      }

      return PaymentResponse(
        status: false,
        message: errorMessage,
      );
    } catch (e) {
      return PaymentResponse(
        status: false,
        message: 'Unexpected error: ${e.toString()}',
      );
    }
  }

  /// Generate a unique merchant reference ID
  String _generateMerchantReference() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'donation_$timestamp';
  }

  /// Get checkout URL for a transaction
  String getCheckoutUrl(String transactionId) {
    return ApiConstants.getCheckoutUrl(transactionId);
  }

  /// Check if URL indicates payment success
  bool isSuccessUrl(String url) {
    return url.contains(ApiConstants.successReturnUrl);
  }

  /// Check if URL indicates payment failure
  bool isFailureUrl(String url) {
    return url.contains(ApiConstants.failedReturnUrl);
  }

  /// Parse payment result from URL
  PaymentResult parsePaymentResult(String url, String? transactionId) {
    if (isSuccessUrl(url)) {
      return PaymentResult(
        status: PaymentStatus.success,
        transactionId: transactionId,
        message: 'Payment completed successfully',
      );
    } else if (isFailureUrl(url)) {
      return PaymentResult(
        status: PaymentStatus.failed,
        transactionId: transactionId,
        message: 'Payment failed or was cancelled',
      );
    } else {
      return PaymentResult(
        status: PaymentStatus.pending,
        transactionId: transactionId,
        message: 'Payment is being processed',
      );
    }
  }
}
