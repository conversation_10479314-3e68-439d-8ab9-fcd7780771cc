import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:jwt_decoder/jwt_decoder.dart';

class TokenService {
  static const _storage = FlutterSecureStorage();
  static const String _tokenKey = 'auth_token';
  static const String _userDataKey = 'user_data';
  
  // Store token and user data
  static Future<void> storeAuthData({
    required String token,
    required Map<String, dynamic> userData,
  }) async {
    await _storage.write(key: _tokenKey, value: token);
    await _storage.write(key: _userDataKey, value: jsonEncode(userData));
  }
  
  // Get stored token
  static Future<String?> getToken() async {
    return await _storage.read(key: _tokenKey);
  }
  
  // Get stored user data
  static Future<Map<String, dynamic>?> getUserData() async {
    final userDataString = await _storage.read(key: _userDataKey);
    if (userDataString != null) {
      return jsonDecode(userDataString);
    }
    return null;
  }
  
  // Check if token is valid and not expired (with 30min buffer)
  static Future<bool> isTokenValid() async {
    final token = await getToken();
    if (token == null) return false;
    
    try {
      // Check if token is expired
      if (JwtDecoder.isExpired(token)) return false;
      
      // Check if token expires within 30 minutes
      final expirationDate = JwtDecoder.getExpirationDate(token);
      final now = DateTime.now();
      final timeUntilExpiry = expirationDate.difference(now);
      
      // If token expires in 30 minutes or less, consider it invalid
      if (timeUntilExpiry.inMinutes <= 30) return false;
      
      return true;
    } catch (e) {
      return false;
    }
  }
  
  // Clear all stored auth data
  static Future<void> clearAuthData() async {
    await _storage.delete(key: _tokenKey);
    await _storage.delete(key: _userDataKey);
  }
  
  // Get bearer token for API calls
  static Future<String?> getBearerToken() async {
    final token = await getToken();
    return token != null ? 'Bearer $token' : null;
  }

  // Update user data only (keep existing token)
  static Future<void> updateUserData(Map<String, dynamic> userData) async {
    await _storage.write(key: _userDataKey, value: jsonEncode(userData));
  }
}