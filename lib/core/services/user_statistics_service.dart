import 'package:dio/dio.dart';
import '../constants/api_constants.dart';
import '../models/api_response.dart';
import '../models/user_statistics.dart';
import 'api_service.dart';

class UserStatisticsService {
  static final UserStatisticsService _instance = UserStatisticsService._internal();
  factory UserStatisticsService() => _instance;
  UserStatisticsService._internal();

  final ApiService _apiService = ApiService();

  /// Get user statistics including donations, charities, and events
  Future<ApiResponse<UserStatistics>> getUserStatistics() async {
    try {
      final response = await _apiService.get(ApiConstants.userStatisticsEndpoint);
      
      final apiResponse = ApiResponse.fromJson(response.data);
      
      if (apiResponse.isSuccess && apiResponse.data != null) {
        final userStatistics = UserStatistics.fromJson(apiResponse.data);
        return ApiResponse<UserStatistics>(
          status: apiResponse.status,
          message: apiResponse.message,
          data: userStatistics,
        );
      } else {
        return ApiResponse<UserStatistics>(
          status: apiResponse.status,
          message: apiResponse.message,
          data: null,
        );
      }
    } on DioException catch (e) {
      String errorMessage = 'Failed to load user statistics. Please try again.';
      if (e.response?.data != null && e.response!.data is Map<String, dynamic>) {
        final responseData = e.response!.data as Map<String, dynamic>;
        errorMessage = responseData['message'] ?? errorMessage;
      }
      return ApiResponse<UserStatistics>(
        status: false,
        message: errorMessage,
        data: null,
      );
    } catch (e) {
      return ApiResponse<UserStatistics>(
        status: false,
        message: 'An unexpected error occurred. Please try again.',
        data: null,
      );
    }
  }


}
