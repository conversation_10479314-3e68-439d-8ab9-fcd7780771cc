import 'package:dio/dio.dart';
import '../constants/api_constants.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'token_service.dart';

class UserUpdateService {
  static final UserUpdateService _instance = UserUpdateService._internal();
  factory UserUpdateService() => _instance;
  UserUpdateService._internal();

  final ApiService _apiService = ApiService();

  /// Update user profile information
  Future<ApiResponse<Map<String, dynamic>>> updateUserProfile({
    String? name,
    String? address,
    String? city,
    String? country,
  }) async {
    try {
      // Build the payload with only non-null values
      final Map<String, dynamic> payload = {};
      
      if (name != null && name.isNotEmpty) payload['name'] = name;
      if (address != null && address.isNotEmpty) payload['address'] = address;
      if (city != null && city.isNotEmpty) payload['city'] = city;
      if (country != null && country.isNotEmpty) payload['country'] = country;

      // If no fields to update, return error
      if (payload.isEmpty) {
        return ApiResponse<Map<String, dynamic>>(
          status: false,
          message: 'No fields to update',
          data: null,
        );
      }

      final response = await _apiService.patch(
        ApiConstants.userUpdateEndpoint,
        data: payload,
      );
      
      final apiResponse = ApiResponse.fromJson(response.data);
      
      if (apiResponse.isSuccess && apiResponse.data != null) {
        final data = apiResponse.data as Map<String, dynamic>;
        
        // Update local storage with new user data
        if (data.containsKey('user')) {
          await TokenService.updateUserData(data['user']);
        }
        
        return ApiResponse<Map<String, dynamic>>(
          status: apiResponse.status,
          message: apiResponse.message,
          data: data,
        );
      } else {
        return ApiResponse<Map<String, dynamic>>(
          status: apiResponse.status,
          message: apiResponse.message,
          data: null,
        );
      }
    } on DioException catch (e) {
      String errorMessage = 'Failed to update profile. Please try again.';
      if (e.response?.data != null && e.response!.data is Map<String, dynamic>) {
        final responseData = e.response!.data as Map<String, dynamic>;
        errorMessage = responseData['message'] ?? errorMessage;
      }
      return ApiResponse<Map<String, dynamic>>(
        status: false,
        message: errorMessage,
        data: null,
      );
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        status: false,
        message: 'An unexpected error occurred. Please try again.',
        data: null,
      );
    }
  }

  /// Handle Dio errors and extract meaningful error messages
  String _handleDioError(DioException e) {
    String errorMessage = 'An unexpected error occurred. Please try again.';

    if (e.response != null) {
      final responseData = e.response!.data;

      // Try to extract error message from API response
      if (responseData is Map<String, dynamic>) {
        if (responseData.containsKey('message')) {
          errorMessage = responseData['message'];
        } else if (responseData.containsKey('error')) {
          errorMessage = responseData['error'];
        } else {
          // Handle different status codes
          switch (e.response!.statusCode) {
            case 400:
              errorMessage = 'Invalid request. Please check your input.';
              break;
            case 401:
              errorMessage = 'Authentication required. Please log in again.';
              break;
            case 403:
              errorMessage = 'Access denied. You don\'t have permission to update this profile.';
              break;
            case 422:
              errorMessage = 'Invalid data provided. Please check your input.';
              break;
            case 500:
              errorMessage = 'Server error. Please try again later.';
              break;
            default:
              errorMessage = 'Server error (${e.response!.statusCode}). Please try again.';
          }
        }
      }
    } else {
      // Handle network errors
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          errorMessage = 'Connection timeout. Please check your internet connection.';
          break;
        case DioExceptionType.connectionError:
          errorMessage = 'Connection failed. Please check your internet connection.';
          break;
        case DioExceptionType.cancel:
          errorMessage = 'Request was cancelled.';
          break;
        default:
          errorMessage = 'Network error. Please check your connection and try again.';
      }
    }

    return errorMessage;
  }
}
