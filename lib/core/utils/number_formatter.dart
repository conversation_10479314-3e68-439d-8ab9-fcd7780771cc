import 'package:intl/intl.dart';

class NumberFormatter {
  static String formatAmount(double amount, {int decimalPlaces = 2}) {
    final formatter = NumberFormat('#,##0.${'0' * decimalPlaces}', 'en_US');
    return formatter.format(amount);
  }

  static String formatAmountWithCurrency(double amount, {String currency = 'SEK', int decimalPlaces = 2}) {
    final formattedAmount = formatAmount(amount, decimalPlaces: decimalPlaces);
    return '$formattedAmount $currency';
  }

  static String formatInteger(int number) {
    final formatter = NumberFormat('#,##0', 'en_US');
    return formatter.format(number);
  }
}
