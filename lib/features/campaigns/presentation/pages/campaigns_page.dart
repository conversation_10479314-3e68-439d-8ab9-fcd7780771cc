import 'package:flutter/material.dart';
import 'package:islamic_charity_app/l10n/app_localizations.dart';
import 'campaign_detail_page.dart';
import 'create_campaign_page.dart';
import '../../../../core/services/campaigns_service.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/models/campaign.dart';
import '../../../../routes/app_routes.dart';

class CampaignsPage extends StatefulWidget {
  const CampaignsPage({super.key});

  @override
  State<CampaignsPage> createState() => _CampaignsPageState();
}

// Global key to access CampaignsPage state from outside
final GlobalKey<_CampaignsPageState> campaignsPageKey = GlobalKey<_CampaignsPageState>();

class _CampaignsPageState extends State<CampaignsPage> {
  final CampaignsService _campaignsService = CampaignsService();
  final AuthService _authService = AuthService();
  List<Campaign> _campaigns = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  int _currentPage = 1;
  bool _hasMorePages = true;
  bool _isAdmin = false;

  @override
  void initState() {
    super.initState();
    _checkUserRole();
    _loadCampaigns();
  }

  Future<void> _checkUserRole() async {
    final userData = await _authService.getCurrentUser();
    final userRole = userData?['role'] as String?;

    setState(() {
      _isAdmin = userRole == 'ADMIN';
    });
  }

  // Public method to refresh campaigns from outside
  Future<void> refreshCampaigns() async {
    await _loadCampaigns(refresh: true);
  }

  Future<void> _loadCampaigns({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _campaigns.clear();
      _hasMorePages = true;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final response = await _campaignsService.getActiveCampaigns(
        page: _currentPage,
        limit: 20,
      );

      if (response.isSuccess && response.data != null) {
        setState(() {
          if (refresh) {
            _campaigns = response.data!.campaigns;
          } else {
            _campaigns.addAll(response.data!.campaigns);
          }
          _hasMorePages = response.data!.hasMorePages;
          _currentPage++;
        });
      } else {
        setState(() {
          _hasError = true;
          _errorMessage = response.message;
        });
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Failed to load campaigns: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.campaigns),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).scaffoldBackgroundColor,
            child: TextField(
              decoration: InputDecoration(
                hintText: l10n.searchCampaigns,
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
            ),
          ),
          // Body
          Expanded(child: _buildBody()),
        ],
      ),
      floatingActionButton: _isAdmin ? FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateCampaignPage(),
            ),
          );
        },
        icon: const Icon(Icons.add),
        label: Text(l10n.newCampaign),
      ) : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading && _campaigns.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_hasError && _campaigns.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              l10n.failedToLoadCampaigns,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadCampaigns(refresh: true),
              child: Text(l10n.retry),
            ),
          ],
        ),
      );
    }

    if (_campaigns.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.campaign_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              l10n.noCampaignsFound,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              l10n.noCampaignsDescription,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadCampaigns(refresh: true),
      child: _campaigns.isEmpty && !_isLoading
          ? Center(
              child: Text(l10n.noCampaignsFound),
            )
          : ListView.separated(
              padding: const EdgeInsets.all(16),
              itemCount: _campaigns.length + (_hasMorePages ? 1 : 0),
              separatorBuilder: (context, index) => const SizedBox(height: 16),
              itemBuilder: (context, index) {
                if (index == _campaigns.length) {
                  // Load more indicator
                  if (_isLoading) {
                    return const Padding(
                      padding: EdgeInsets.all(16),
                      child: Center(child: CircularProgressIndicator()),
                    );
                  } else {
                    return Padding(
                      padding: const EdgeInsets.all(16),
                      child: ElevatedButton(
                        onPressed: _loadCampaigns,
                        child: Text(l10n.viewAll),
                      ),
                    );
                  }
                }

                final campaign = _campaigns[index];
                return _buildCampaignCard(campaign);
              },
            ),
    );
  }

  Widget _buildCampaignCard(Campaign campaign) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CampaignDetailPage(campaign: campaign),
          ),
        );
      },
      child: Card(
        margin: EdgeInsets.zero,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(12),
              ),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                  ),
                  child: campaign.imageUrl != null
                      ? Image.network(
                          campaign.imageUrl!,
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Image.asset(
                              'assets/images/ph-image.jpg',
                              width: double.infinity,
                              height: double.infinity,
                              fit: BoxFit.cover,
                            );
                          },
                        )
                      : Image.asset(
                          'assets/images/ph-image.jpg',
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                        ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    campaign.title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    campaign.description,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[700],
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'By ${campaign.user.name}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Started: ${campaign.formattedStartDate}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 16),
                  LinearProgressIndicator(
                    value: campaign.progressPercentage,
                    backgroundColor: Colors.grey[200],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).colorScheme.primary,
                    ),
                    minHeight: 8,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          '${campaign.formattedRaisedAmount} raised of ${campaign.formattedGoalAmount}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        campaign.formattedDaysLeft,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: campaign.daysLeft > 7
                              ? Colors.green[600]
                              : campaign.daysLeft > 0
                                  ? Colors.orange[600]
                                  : Colors.red[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          AppRoutes.donate,
                          arguments: {
                            'campaignId': campaign.id,
                            'campaignTitle': campaign.title,
                          },
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Donate Now',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

}
