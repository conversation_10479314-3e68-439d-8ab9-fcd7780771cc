import 'package:flutter/material.dart';
import 'package:islamic_charity_app/routes/app_routes.dart';
import 'package:islamic_charity_app/l10n/app_localizations.dart';
import '../../../../core/services/campaigns_service.dart';
import '../../../../core/models/campaign.dart';

class HomePage extends StatefulWidget {
  final bool showBottomNav;
  final VoidCallback? onNavigateToCampaigns;

  const HomePage({
    super.key,
    this.showBottomNav = true,
    this.onNavigateToCampaigns,
  });

  @override
  State<HomePage> createState() => _HomePageState();
}

// Global key to access HomePage state from outside
final GlobalKey<_HomePageState> homePageKey = GlobalKey<_HomePageState>();

class _HomePageState extends State<HomePage> {
  final CampaignsService _campaignsService = CampaignsService();
  List<Campaign> _featuredCampaigns = [];
  bool _isLoadingCampaigns = false;

  @override
  void initState() {
    super.initState();
    _loadFeaturedCampaigns();
  }

  Future<void> refreshCampaigns() async {
    await _loadFeaturedCampaigns();
  }

  Future<void> _loadFeaturedCampaigns() async {
    setState(() {
      _isLoadingCampaigns = true;
    });

    try {
      final response = await _campaignsService.getFeaturedCampaigns();
      if (response.isSuccess && response.data != null) {
        setState(() {
          _featuredCampaigns = response.data!.campaigns;
        });
      }
    } catch (e) {
      // Handle error silently for now
      print('Error loading featured campaigns: $e');
    } finally {
      setState(() {
        _isLoadingCampaigns = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final mediaQuery = MediaQuery.of(context);
    final screenHeight = mediaQuery.size.height;
    final topPadding = mediaQuery.padding.top;

    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.only(
                  left: 16,
                  right: 16,
                  top: widget.showBottomNav ? 16 : (topPadding > 0 ? 8 : 16),
                  bottom: 16,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildWelcomeSection(context, l10n),
                    SizedBox(height: screenHeight * 0.03), // Responsive spacing
                    _buildActiveFeatures(context, l10n),
                    SizedBox(height: screenHeight * 0.04), // Responsive spacing
                    _buildFeaturedCampaignsSection(context, l10n),
                    SizedBox(height: screenHeight * 0.04), // Responsive spacing
                    _buildComingSoonSection(context, l10n),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: widget.showBottomNav ? _buildBottomNav(context, l10n) : null,
    );
  }

  Widget _buildWelcomeSection(BuildContext context, AppLocalizations l10n) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;

    // Responsive logo size based on screen dimensions
    final logoSize = (screenWidth * 0.3).clamp(120.0, 180.0);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(screenWidth * 0.05), // Responsive padding
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withOpacity(0.1),
            Theme.of(context).colorScheme.secondary.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Container(
            width: logoSize,
            height: logoSize,
            padding: EdgeInsets.all(logoSize * 0.1), // Responsive padding
            child: Image.asset(
              'assets/images/applogo.png',
              fit: BoxFit.contain,
            ),
          ),
          SizedBox(height: screenHeight * 0.015), // Responsive spacing
          Text(
            l10n.welcomeToAkhuwat,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: (screenWidth * 0.055).clamp(18.0, 24.0), // Responsive font size
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: screenHeight * 0.01), // Responsive spacing
          Text(
            l10n.makeDifferenceWithContributions,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              fontSize: (screenWidth * 0.04).clamp(14.0, 16.0), // Responsive font size
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildActiveFeatures(BuildContext context, AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.availableFeatures,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildFeatureCard(
                context,
                icon: Icons.volunteer_activism,
                title: l10n.donate,
                subtitle: l10n.makeYourContribution,
                color: Theme.of(context).colorScheme.primary,
                onTap: () => Navigator.pushNamed(context, AppRoutes.donate),
                isActive: true,
              ),
            ),
            SizedBox(width: MediaQuery.of(context).size.width * 0.04), // Responsive spacing
            Expanded(
              child: _buildFeatureCard(
                context,
                icon: Icons.calculate,
                title: l10n.zakatCalculator,
                subtitle: l10n.calculateYourZakat,
                color: Theme.of(context).colorScheme.secondary,
                onTap: () => Navigator.pushNamed(context, AppRoutes.zakatCalculator),
                isActive: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFeaturedCampaignsSection(BuildContext context, AppLocalizations l10n) {
    if (_featuredCampaigns.isEmpty && !_isLoadingCampaigns) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              l10n.featuredCampaigns,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: widget.onNavigateToCampaigns ?? () => Navigator.pushNamed(context, AppRoutes.campaigns),
              child: Text(
                l10n.viewAll,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_isLoadingCampaigns)
          const Center(
            child: CircularProgressIndicator(),
          )
        else
          SizedBox(
            height: 260,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.only(right: 16),
              itemCount: _featuredCampaigns.length,
              separatorBuilder: (context, index) => const SizedBox(width: 16),
              itemBuilder: (context, index) {
                final campaign = _featuredCampaigns[index];
                return _buildCompactCampaignCard(context, campaign);
              },
            ),
          ),
      ],
    );
  }

  Widget _buildCompactCampaignCard(BuildContext context, Campaign campaign) {
    return InkWell(
      onTap: widget.onNavigateToCampaigns ?? () => Navigator.pushNamed(context, AppRoutes.campaigns),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 280,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.15),
              spreadRadius: 2,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Campaign image with progress badge
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                  child: SizedBox(
                    height: 130,
                    width: double.infinity,
                    child: campaign.imageUrl != null
                        ? Image.network(campaign.imageUrl!, fit: BoxFit.cover, errorBuilder: (_, __, ___) {
                      return Image.asset('assets/images/ph-image.jpg', fit: BoxFit.cover);
                    })
                        : Image.asset('assets/images/ph-image.jpg', fit: BoxFit.cover),
                  ),
                ),
                // Gradient overlay for better text readability
                Container(
                  height: 130,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.3),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${(campaign.progressPercentage * 100).toInt()}%',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // Content section
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(14),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      campaign.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: 15,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 6),

                    // Author
                    Row(
                      children: [
                        Icon(Icons.person, size: 14, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Expanded(
                                                  child: Text(
                          '${l10n.by} ${campaign.user.name}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        ),
                      ],
                    ),

                    const Spacer(),

                    // Progress section
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Progress bar
                        LinearProgressIndicator(
                          value: campaign.progressPercentage,
                          backgroundColor: Colors.grey[200],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).colorScheme.primary,
                          ),
                          minHeight: 6,
                          borderRadius: BorderRadius.circular(3),
                        ),
                        const SizedBox(height: 8),

                        // Amount & Days Left
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  campaign.formattedRaisedAmount,
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).colorScheme.primary,
                                    fontSize: 16,
                                  ),
                                ),
                                Text(
                                  '${l10n.of} ${campaign.formattedGoalAmount}',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                    fontSize: 11,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: campaign.daysLeft > 7
                                    ? Colors.green[50]
                                    : campaign.daysLeft > 0
                                        ? Colors.orange[50]
                                        : Colors.red[50],
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: campaign.daysLeft > 7
                                      ? Colors.green[200]!
                                      : campaign.daysLeft > 0
                                          ? Colors.orange[200]!
                                          : Colors.red[200]!,
                                ),
                              ),
                              child: Text(
                                campaign.formattedDaysLeft,
                                style: TextStyle(
                                  fontSize: 11,
                                  color: campaign.daysLeft > 7
                                      ? Colors.green[700]
                                      : campaign.daysLeft > 0
                                          ? Colors.orange[700]
                                          : Colors.red[700],
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComingSoonSection(BuildContext context, AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              l10n.comingSoon,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                l10n.beta,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // Quick access cards for coming soon features
        Row(
          children: [
            Expanded(
              child: _buildSmallFeatureCard(
                context,
                icon: Icons.event,
                title: l10n.events,
                onTap: () => _showComingSoonDialog(context, l10n.events),
              ),
            ),
            SizedBox(width: MediaQuery.of(context).size.width * 0.04), // Responsive spacing
            Expanded(
              child: _buildSmallFeatureCard(
                context,
                icon: Icons.volunteer_activism_outlined,
                title: l10n.charities,
                onTap: () => _showComingSoonDialog(context, l10n.charities),
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),
        
        // Top Charities Slider
        _buildComingSoonSlider(
          context,
          title: l10n.topCharities,
          items: [
            _buildCharityCard(context, l10n.islamicRelief, l10n.globalHumanitarianAid, '4.8★'),
            _buildCharityCard(context, l10n.localMosqueFund, l10n.communitySupport, '4.9★'),
            _buildCharityCard(context, l10n.educationFoundation, l10n.studentScholarships, '4.7★'),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Upcoming Events Slider
        _buildComingSoonSlider(
          context,
          title: l10n.upcomingEvents,
          items: [
            _buildEventCard(context, l10n.charityGala, l10n.march252024, l10n.fundraisingEvent),
            _buildEventCard(context, l10n.communityIftar, l10n.april152024, l10n.ramadanSpecial),
            _buildEventCard(context, l10n.volunteerDrive, l10n.may102024, l10n.communityService),
          ],
        ),
      ],
    );
  }

  Widget _buildSmallFeatureCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;

    // Responsive dimensions
    final cardHeight = (screenHeight * 0.1).clamp(70.0, 90.0);
    final iconSize = (screenWidth * 0.05).clamp(18.0, 24.0);
    final badgeSize = (screenWidth * 0.015).clamp(5.0, 8.0);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        height: cardHeight,
        padding: EdgeInsets.all(screenWidth * 0.02), // Responsive padding
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              children: [
                Icon(icon, size: iconSize, color: Colors.grey),
                Positioned(
                  right: -2,
                  top: -2,
                  child: Container(
                    padding: const EdgeInsets.all(1),
                    decoration: const BoxDecoration(
                      color: Colors.orange,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(Icons.schedule, size: badgeSize, color: Colors.white),
                  ),
                ),
              ],
            ),
            SizedBox(height: screenHeight * 0.008), // Responsive spacing
            Flexible(
              child: Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Colors.grey,
                  fontSize: (screenWidth * 0.028).clamp(10.0, 12.0), // Responsive font size
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComingSoonSlider(BuildContext context, {required String title, required List<Widget> items}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                l10n.comingSoon,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Colors.orange[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 140,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: items.length,
            separatorBuilder: (context, index) => const SizedBox(width: 12),
            itemBuilder: (context, index) => items[index],
          ),
        ),
      ],
    );
  }


  Widget _buildCharityCard(BuildContext context, String name, String description, String rating) {
    return InkWell(
      onTap: () => _showComingSoonDialog(context, l10n.charities),
      child: Container(
        width: 160,
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.withOpacity(0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(Icons.volunteer_activism, color: Colors.grey, size: 20),
                const Spacer(),
                Text(
                  rating, 
                  style: TextStyle(
                    color: Colors.orange, 
                    fontWeight: FontWeight.bold,
                    fontSize: 11,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name, 
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Flexible(
                    child: Text(
                      description, 
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey,
                        fontSize: 10,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventCard(BuildContext context, String title, String date, String type) {
    return InkWell(
      onTap: () => _showComingSoonDialog(context, l10n.events),
      child: Container(
        width: 140,
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.withOpacity(0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Icon(Icons.event, color: Colors.grey, size: 20),
            const SizedBox(height: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title, 
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    date, 
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey,
                      fontSize: 10,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    type, 
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.blue,
                      fontSize: 10,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    required bool isActive,
  }) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;

    // Responsive dimensions
    final cardHeight = (screenHeight * 0.18).clamp(120.0, 160.0);
    final iconSize = (screenWidth * 0.08).clamp(32.0, 48.0);

    return InkWell(
      onTap: isActive ? onTap : null,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        height: cardHeight,
        padding: EdgeInsets.all(screenWidth * 0.04), // Responsive padding
        decoration: BoxDecoration(
          color: isActive
              ? color.withOpacity(0.1)
              : Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isActive
                ? color.withOpacity(0.3)
                : Colors.grey.withOpacity(0.3),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: iconSize, color: isActive ? color : Colors.grey),
            SizedBox(height: screenHeight * 0.015), // Responsive spacing
            Flexible(
              child: Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isActive ? null : Colors.grey,
                  fontSize: (screenWidth * 0.04).clamp(14.0, 18.0), // Responsive font size
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(height: screenHeight * 0.005), // Responsive spacing
            Flexible(
              child: Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: isActive ? Colors.grey[600] : Colors.grey,
                  fontSize: (screenWidth * 0.032).clamp(12.0, 14.0), // Responsive font size
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNav(BuildContext context, AppLocalizations l10n) {
    return NavigationBar(
      destinations: [
        NavigationDestination(
          icon: const Icon(Icons.home_outlined),
          selectedIcon: const Icon(Icons.home),
          label: l10n.home,
        ),
        NavigationDestination(
          icon: const Icon(Icons.volunteer_activism_outlined),
          selectedIcon: const Icon(Icons.volunteer_activism),
          label: l10n.campaigns,
        ),
        NavigationDestination(
          icon: Stack(
            children: [
              const Icon(Icons.event_outlined),
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: Colors.orange,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.schedule,
                    size: 8,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          selectedIcon: const Icon(Icons.event),
          label: l10n.events,
        ),
        NavigationDestination(
          icon: const Icon(Icons.person_outline),
          selectedIcon: const Icon(Icons.person),
          label: l10n.profile,
        ),
      ],
      onDestinationSelected: (index) {
        switch (index) {
          case 0:
            break;
          case 1:
            Navigator.pushNamed(context, AppRoutes.campaigns);
            break;
          case 2:
            _showComingSoonDialog(context, l10n.events);
            break;
          case 3:
            Navigator.pushNamed(context, AppRoutes.profile);
            break;
        }
      },
    );
  }

  void _showComingSoonDialog(BuildContext context, String featureName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(
          Icons.construction,
          size: 48,
          color: Colors.orange,
        ),
        title: Text(l10n.comingSoonFeature),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '$featureName ${l10n.featureUnderDevelopment}',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              l10n.workingHardToBringFeature,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.gotIt),
          ),
        ],
      ),
    );
  }
}
