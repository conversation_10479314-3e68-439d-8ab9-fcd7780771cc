import 'package:flutter/material.dart';
import 'package:islamic_charity_app/l10n/app_localizations.dart';

class NotificationsPage extends StatelessWidget {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.notifications),
      ),
      body: ListView(
        children: [
          _buildNotificationSection(
            context,
            l10n.donationUpdates,
            [
              _buildNotificationItem(
                context,
                'Your donation of SEK 100 has been received',
                '2 hours ago',
                Icons.favorite,
                Colors.red,
              ),
              _buildNotificationItem(
                context,
                'Campaign "Help the Needy" has reached 50% of its goal',
                '1 day ago',
                Icons.celebration,
                Colors.orange,
              ),
            ],
          ),
          _buildNotificationSection(
            context,
            l10n.campaignUpdates,
            [
              _buildNotificationItem(
                context,
                'New campaign "Education for All" has been launched',
                '3 days ago',
                Icons.school,
                Colors.blue,
              ),
              _buildNotificationItem(
                context,
                'Your campaign "Food Drive" has been approved',
                '1 week ago',
                Icons.check_circle,
                Colors.green,
              ),
            ],
          ),
          _buildNotificationSection(
            context,
            l10n.systemNotifications,
            [
              _buildNotificationItem(
                context,
                'Your account has been verified',
                '2 weeks ago',
                Icons.verified,
                Colors.purple,
              ),
              _buildNotificationItem(
                context,
                'Welcome to Islamic Charity App!',
                '1 month ago',
                Icons.waving_hand,
                Colors.teal,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSection(
    BuildContext context,
    String title,
    List<Widget> notifications,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        ...notifications,
        const Divider(),
      ],
    );
  }

  Widget _buildNotificationItem(
    BuildContext context,
    String message,
    String time,
    IconData icon,
    Color color,
  ) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withOpacity(0.1),
        child: Icon(icon, color: color),
      ),
      title: Text(message),
      subtitle: Text(
        time,
        style: Theme.of(context).textTheme.bodySmall,
      ),
      trailing: IconButton(
        icon: const Icon(Icons.more_vert),
        onPressed: () {
          // Show notification options
          showModalBottomSheet(
            context: context,
            builder: (context) => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.check),
                  title: const Text('Mark as read'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Implement mark as read functionality
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.delete),
                  title: const Text('Delete notification'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Implement delete functionality
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
