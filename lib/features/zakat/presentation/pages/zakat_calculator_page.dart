import 'package:flutter/material.dart';
import 'package:islamic_charity_app/l10n/app_localizations.dart';
import 'package:hijri/hijri_calendar.dart';

class ZakatCalculatorPage extends StatefulWidget {
  const ZakatCalculatorPage({super.key});

  @override
  State<ZakatCalculatorPage> createState() => _ZakatCalculatorPageState();
}

class _ZakatCalculatorPageState extends State<ZakatCalculatorPage> {
  final _formKey = GlobalKey<FormState>();
  final _cashController = TextEditingController();
  final _goldController = TextEditingController();
  final _silverController = TextEditingController();
  final _stocksController = TextEditingController();
  final _businessController = TextEditingController();
  final _debtsController = TextEditingController();
  final _loansGivenController = TextEditingController();
  final _propertyController = TextEditingController();
  final _savingsController = TextEditingController();
  
  double _totalZakat = 0;
  double _nisabAmount = 0;
  bool _isCalculated = false;
  bool _isEligibleForZakat = false;
  
  // Current Nisab values (should be updated regularly)
  static const double goldNisab = 87.48; // grams of gold
  static const double silverNisab = 612.36; // grams of silver
  static const double goldPricePerGram = 65.0; // USD per gram (example)
  static const double silverPricePerGram = 0.85; // USD per gram (example)

  @override
  void dispose() {
    _cashController.dispose();
    _goldController.dispose();
    _silverController.dispose();
    _stocksController.dispose();
    _businessController.dispose();
    _debtsController.dispose();
    _loansGivenController.dispose();
    _propertyController.dispose();
    _savingsController.dispose();
    super.dispose();
  }

  void _calculateZakat() {
    if (_formKey.currentState!.validate()) {
      final cash = double.tryParse(_cashController.text) ?? 0;
      final gold = double.tryParse(_goldController.text) ?? 0;
      final silver = double.tryParse(_silverController.text) ?? 0;
      final stocks = double.tryParse(_stocksController.text) ?? 0;
      final business = double.tryParse(_businessController.text) ?? 0;
      final debts = double.tryParse(_debtsController.text) ?? 0;
      final loansGiven = double.tryParse(_loansGivenController.text) ?? 0;
      final property = double.tryParse(_propertyController.text) ?? 0;
      final savings = double.tryParse(_savingsController.text) ?? 0;

      // Calculate total zakatable assets
      final totalAssets = cash + gold + silver + stocks + business + loansGiven + property + savings;

      // Calculate total liabilities (immediate debts only)
      final totalLiabilities = debts;

      // Calculate net zakatable wealth
      final netWorth = totalAssets - totalLiabilities;

      // Calculate Nisab (using gold standard as it's typically higher)
      final goldNisabValue = goldNisab * goldPricePerGram;
      final silverNisabValue = silverNisab * silverPricePerGram;
      _nisabAmount = goldNisabValue; // Using gold nisab as standard

      // Check if wealth meets Nisab threshold
      _isEligibleForZakat = netWorth >= _nisabAmount;

      // Calculate Zakat (2.5% of net worth if above Nisab)
      setState(() {
        if (_isEligibleForZakat) {
          _totalZakat = netWorth * 0.025;
        } else {
          _totalZakat = 0;
        }
        _isCalculated = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final hijri = HijriCalendar.now();

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.zakatCalculator),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showZakatInfoDialog(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildIslamicYearCard(hijri),
            const SizedBox(height: 16),
            _buildNisabInfoCard(),
            const SizedBox(height: 24),
            Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.enterYourAssets,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  _buildAmountField(
                    controller: _cashController,
                    label: l10n.cashInHand,
                    icon: Icons.money,
                    hint: 'Cash, bank accounts, etc.',
                  ),
                  const SizedBox(height: 16),
                  _buildAmountField(
                    controller: _savingsController,
                    label: 'Savings & Deposits',
                    icon: Icons.savings,
                    hint: 'Fixed deposits, savings accounts',
                  ),
                  const SizedBox(height: 16),
                  _buildAmountField(
                    controller: _goldController,
                    label: l10n.goldValue,
                    icon: Icons.monetization_on,
                    hint: 'Gold jewelry, coins, bars',
                  ),
                  const SizedBox(height: 16),
                  _buildAmountField(
                    controller: _silverController,
                    label: l10n.silverValue,
                    icon: Icons.monetization_on,
                    hint: 'Silver jewelry, coins, bars',
                  ),
                  const SizedBox(height: 16),
                  _buildAmountField(
                    controller: _stocksController,
                    label: l10n.stocksValue,
                    icon: Icons.show_chart,
                    hint: 'Shares, mutual funds, bonds',
                  ),
                  const SizedBox(height: 16),
                  _buildAmountField(
                    controller: _businessController,
                    label: l10n.businessValue,
                    icon: Icons.business,
                    hint: 'Business inventory, trade goods',
                  ),
                  const SizedBox(height: 16),
                  _buildAmountField(
                    controller: _propertyController,
                    label: 'Investment Property',
                    icon: Icons.home,
                    hint: 'Rental property, land for investment',
                  ),
                  const SizedBox(height: 16),
                  _buildAmountField(
                    controller: _loansGivenController,
                    label: 'Loans Given to Others',
                    icon: Icons.account_balance_wallet,
                    hint: 'Money lent that you expect back',
                  ),
                  const SizedBox(height: 24),
                  Text(
                    l10n.enterYourLiabilities,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  _buildAmountField(
                    controller: _debtsController,
                    label: l10n.debts,
                    icon: Icons.money_off,
                    hint: 'Immediate debts, loans, credit cards',
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _calculateZakat,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(l10n.calculateZakat),
                    ),
                  ),
                ],
              ),
            ),
            if (_isCalculated) ...[
              const SizedBox(height: 24),
              _buildZakatResult(context),
            ],
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildIslamicYearCard(HijriCalendar hijri) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.mosque,
              size: 32,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Islamic Year: ${hijri.hYear} AH',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${hijri.getLongMonthName()} ${hijri.hDay}',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNisabInfoCard() {
    return Card(
      color: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info,
                  color: Theme.of(context).colorScheme.secondary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Nisab Threshold',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Current Nisab: SEK ${_nisabAmount.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.secondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Nisab is the minimum amount of wealth required to be eligible for Zakat. It equals the value of ${goldNisab}g of gold.',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? hint,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon),
        prefixText: 'SEK ',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          if (double.tryParse(value) == null) {
            return 'Please enter a valid number';
          }
        }
        return null;
      },
    );
  }

  Widget _buildZakatResult(BuildContext context) {
    return Card(
      color: _isEligibleForZakat 
          ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
          : Colors.orange.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isEligibleForZakat ? Icons.check_circle : Icons.info,
                  color: _isEligibleForZakat 
                      ? Theme.of(context).colorScheme.primary
                      : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  _isEligibleForZakat ? 'Your Zakat Amount' : 'Zakat Status',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isEligibleForZakat) ...[
              Text(
                'SEK ${_totalZakat.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'This is 2.5% of your zakatable wealth',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'Your wealth exceeds the Nisab threshold, so Zakat is obligatory.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.green[700],
                ),
              ),
            ] else ...[
              Text(
                'No Zakat Due',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.orange[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Your wealth is below the Nisab threshold of SEK ${_nisabAmount.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'Zakat is not obligatory, but voluntary charity (Sadaqah) is always encouraged.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.orange[700],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showZakatInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About Zakat'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Zakat is one of the Five Pillars of Islam and is obligatory for Muslims who meet certain criteria:',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 12),
              _buildInfoPoint('• Must be a Muslim'),
              _buildInfoPoint('• Must be of sound mind'),
              _buildInfoPoint('• Must have reached puberty'),
              _buildInfoPoint('• Wealth must exceed Nisab threshold'),
              _buildInfoPoint('• Wealth must be held for one lunar year'),
              const SizedBox(height: 12),
              Text(
                'Important Notes:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              _buildInfoPoint('• Personal residence is not zakatable'),
              _buildInfoPoint('• Personal use items are not zakatable'),
              _buildInfoPoint('• Debts reduce zakatable wealth'),
              _buildInfoPoint('• Consult a scholar for complex situations'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall,
      ),
    );
  }
}