import 'package:flutter/material.dart';
import '../../core/services/auth_service.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import 'home_screen.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: AuthService().isAuthenticated(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
        
        final isAuthenticated = snapshot.data ?? false;
        return isAuthenticated ? const HomeScreen() : const LoginPage();
      },
    );
  }
}