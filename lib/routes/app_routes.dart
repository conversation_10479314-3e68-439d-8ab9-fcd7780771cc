import 'package:flutter/material.dart';
import '../core/presentation/pages/main_navigation_page.dart';
import '../features/home/<USER>/pages/home_page.dart';
import '../features/auth/presentation/pages/login_page.dart';
import '../features/auth/presentation/pages/register_page.dart';
import '../features/charities/presentation/pages/charities_page.dart';
import '../features/campaigns/presentation/pages/campaigns_page.dart';
import '../features/events/presentation/pages/events_page.dart';
import '../features/profile/presentation/pages/profile_page.dart';
import '../features/donate/presentation/pages/donate_page.dart';
import '../features/zakat/presentation/pages/zakat_calculator_page.dart';
import '../features/about/presentation/pages/about_page.dart';
import '../features/payment/presentation/pages/payment_webview_page.dart';
import '../features/payment/presentation/pages/payment_result_page.dart';
import '../features/notifications/presentation/pages/notifications_page.dart';
import '../features/reports/presentation/pages/reports_page.dart';
import 'package:islamic_charity_app/core/presentation/pages/splash_screen.dart';

class AppRoutes {
  static const String splash = '/';
  static const String home = '/home';
  static const String login = '/login';
  static const String register = '/register';
  static const String profile = '/profile';
  static const String charities = '/charities';
  static const String campaigns = '/campaigns';
  static const String events = '/events';
  static const String donate = '/donate';
  static const String zakatCalculator = '/zakat-calculator';
  static const String about = '/about';
  static const String notifications = '/notifications';
  static const String reports = '/reports';
  static const String paymentWebview = '/payment-webview';
  static const String paymentResult = '/payment-result';

  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(builder: (_) => const SplashScreen());
      case home:
        return MaterialPageRoute(builder: (_) => const MainNavigationPage());
      case login:
        return MaterialPageRoute(builder: (_) => const LoginPage());
      case register:
        return MaterialPageRoute(builder: (_) => const RegisterPage());
      case profile:
        return MaterialPageRoute(builder: (_) => const ProfilePage());
      case charities:
        return MaterialPageRoute(builder: (_) => const CharitiesPage());
      case campaigns:
        return MaterialPageRoute(builder: (_) => const CampaignsPage());
      case events:
        return MaterialPageRoute(builder: (_) => const EventsPage());
      case donate:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => DonatePage(
            campaignId: args?['campaignId'],
            campaignTitle: args?['campaignTitle'],
          ),
        );
      case zakatCalculator:
        return MaterialPageRoute(builder: (_) => const ZakatCalculatorPage());
      case about:
        return MaterialPageRoute(builder: (_) => const AboutPage());
      case notifications:
        return MaterialPageRoute(builder: (_) => const NotificationsPage());
      case reports:
        return MaterialPageRoute(builder: (_) => const ReportsPage());
      case paymentWebview:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => PaymentWebViewPage(
            transactionId: args['transactionId'],
            amount: args['amount'],
            donationType: args['donationType'],
          ),
        );
      case paymentResult:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => PaymentResultPage(
            result: args['result'],
            amount: args['amount'],
            donationType: args['donationType'],
          ),
        );
      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(
              child: Text('No route defined for ${settings.name}'),
            ),
          ),
        );
    }
  }
}
